input SalesOfferModuleEmailContentsInput {
    combinedTemplate: SalesOfferEmailContentsInput!
    singlePreOfferTemplate: SalesOfferSinglePreOfferTemplateEmailContentInput!
    salesOfferTemplate: SalesOfferEmailContentsInput!
}

input SalesOfferSinglePreOfferTemplateEmailContentInput {
    coeBiddingTemplate: SalesOfferEmailContentsInput!
    specificationTemplate: SalesOfferEmailContentsInput!
    tradeInAgreementTemplate: SalesOfferEmailContentsInput!
}

input SalesOfferEmailContentsInput {
    subject: DealerTranslatedStringSettingInput!
    introTitle: DealerTranslatedStringSettingInput!
    contentText: DealerTranslatedStringSettingInput!
}

input SalesOfferModuleDealerSpecificEmailContentInput {
    dealerId: ObjectID
    emailContentUpdateType: EmailContentUpdateType!
    combinedTemplate: SalesOfferEmailContentsInput!
    singlePreOfferTemplate: SalesOfferEmailContentsInput!
    salesOfferTemplate: SalesOfferEmailContentsInput!
}
