type SalesOfferApplication implements Application {
    """
    UID
    """
    id: ObjectID!

    stages: [ApplicationStage!]!
    mobilityStage: MobilityStage
    reservationStage: ReservationStage
    financingStage: FinancingStage
    appointmentStage: AppointmentStage
    insuranceStage: InsuranceStage
    latestStage: ApplicationStageDetails
    visitAppointmentStage: VisitAppointmentStage
    tradeInStage: TradeInStage

    """
    Agreements for the applicant
    """
    applicantAgreements: [ApplicationAgreement!]!
    permissions: [String]!
    """
    KYC fields for the applicant
    """
    applicantKYC: [KYCField!]!

    """
    Agreements for the corporate
    """
    corporateAgreements: [ApplicationAgreement!]!

    """
    KYC fields for the corporate
    """
    corporateKYC: [KYCField!]!

    """
    Agreements for the guarantor
    """
    guarantorAgreements: [ApplicationAgreement!]!

    """
    KYC fields for the guarantor
    """
    guarantorKYC: [KYCField!]!

    """
    Drafting flow
    """
    draftFlow: StandardApplicationDraftFlow!

    """
    ModuleId
    """
    moduleId: ObjectID!

    """
    Module
    """
    module: Module!

    """
    Versioning
    """
    versioning: AdvancedVersioning!

    assigneeId: ObjectID
    assignee: User

    """
    assignees this application can be assigned to
    """
    availableAssignees: [User!]!

    """
    Vehicle ID
    """
    vehicleId: ObjectID

    """
    Vehicle
    """
    vehicle: Vehicle

    """
    Applicant ID
    """
    applicantId: ObjectID!

    """
    Applicant
    """
    applicant: Customer!

    """
    Guarantor
    currently only have 1 Guarantor
    might have more guarantor in future
    """
    guarantor: Customer

    """
    Bank ID
    """
    bankId: ObjectID

    """
    Bank
    """
    bank: SystemBank

    """
    Financial Product
    """
    financeProduct: FinanceProduct

    """
    Application Finance
    """
    financing: ApplicationFinancing

    """
    Insurer
    """
    insurer: Insurer

    """
    Application insurancing
    """
    insurancing: ApplicationInsurancing

    """
    Application Configuration
    """
    configuration: SalesOfferApplicationConfiguration!

    """
    Application Document
    """
    documents: [ApplicationDocument!]!

    """
    Application Deposit
    """
    deposit: ApplicationDeposit

    """
    Application Signing
    """
    signing: ApplicationSigning

    """
    Insurance application signing
    """
    insuranceSigning: ApplicationSigning

    """
    Insurance Product
    """
    insuranceProduct: InsuranceProduct

    """
    Dealer ID
    """
    dealerId: ObjectID!

    """
    Dealer data
    """
    dealer: Dealer!

    """
    Router ID
    """
    routerId: ObjectID

    """
    Endpoint
    """
    endpoint: Endpoint

    """
    Router
    """
    router: Router

    """
    Other Vehicle Information
    """
    otherVehicleInformation: OtherVehicleInformation

    remarks: String!

    """
    Language ID to determine what language user chose in journey
    """
    languageId: ObjectID

    """
    Journey Steps Identifiers
    """
    journeySteps: [String!]!

    """
    Lead ID
    """
    leadId: ObjectID!

    """
    Lead
    """
    lead: Lead!

    """
    Details URL
    """
    detailUrl: String
}
