import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type SalesOfferModuleDebugJourneyFragment = (
  { __typename: 'SalesOfferModule' }
  & Pick<SchemaTypes.SalesOfferModule, 'id'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
  ) }
);

export const SalesOfferModuleDebugJourneyFragmentDoc = /*#__PURE__*/ gql`
    fragment SalesOfferModuleDebugJourney on SalesOfferModule {
  __typename
  id
  company {
    timeZone
    countryCode
  }
  __typename
}
    `;