import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type SalesOfferApplicationConfigurationDataFragment = (
  { __typename: 'SalesOfferApplicationConfiguration' }
  & Pick<SchemaTypes.SalesOfferApplicationConfiguration, 'withFinancing' | 'withInsurance'>
);

export const SalesOfferApplicationConfigurationDataFragmentDoc = /*#__PURE__*/ gql`
    fragment SalesOfferApplicationConfigurationData on SalesOfferApplicationConfiguration {
  withFinancing
  withInsurance
}
    `;