fragment SalesOfferApplicationData on SalesOfferApplication {
    ...ApplicationStageData
    applicant {
        ...CustomerSpecs
    }

    guarantor {
        ...CustomerSpecs
    }

    # Draft flow used for check applying for finance/insurance condition
    draftFlow {
        ...DraftFlowConfigurationSpec
    }

    vehicleId
    vehicle {
        ...VehicleSpecs
    }

    otherVehicleInformation {
        chassisNo
        engineNo
    }

    module {
        ...LaunchpadModuleSpecsForApplication
        company {
            timeZone
        }
    }

    versioning {
        ...AdvancedVersioningData
    }

    configuration {
        ...SalesOfferApplicationConfigurationData
    }


    documents {
        ...ApplicationDocumentData
    }


    assignee {
        ...UsersOptionsData
    }

    availableAssignees {
        ...UsersOptionsData
    }

    deposit {
        ... on ApplicationAdyenDeposit {
            ...ApplicationAdyenDepositData
        }

        ... on ApplicationPorscheDeposit {
            ...ApplicationPorscheDepositData
        }

        ... on ApplicationFiservDeposit {
            ...ApplicationFiservDepositData
        }

        ... on ApplicationPayGateDeposit {
            ...ApplicationPayGateDepositData
        }

        ... on ApplicationTtbDeposit {
            ...ApplicationTtbDepositData
        }
    }

    dealer {
        ...DealerApplicationFragment
    }

    routerId

    router {
        id
        pathname
    }
    endpoint {
        ...EndpointContextData
    }

    applicantKYC {
        ...KYCFieldSpecs
    }
    applicantAgreements {
        ...ApplicationAgreementData
    }

    lead {
        ...LeadData
    }

    bankId
    bank {
        ...BankDetailsData
    }

    financing {
        ...ApplicationFinancingData
    }

    financeProduct {
        ...FinanceProductDetails
    }

    insurancing {
        ...ApplicationInsurancingData
    }

    insuranceProduct {
        ...InsuranceProductDetailsData
    }

    insurer {
        id
        displayName
        legalName {
            ...TranslatedStringData
        }
        showCommentsField
    }

    guarantorKYC {
        ...KYCFieldSpecs
    }

    guarantorAgreements {
        ...ApplicationAgreementData
    }

}
