fragment CustomerDetailsSalesOfferApplication on SalesOfferApplication {
    id
    stages
    module {
        displayName
        company {
            displayName
            timeZone
            currency
            roundings {
                amount {
                    decimals
                }
                percentage {
                    decimals
                }
            }
        }
    }
    dealer {
        displayName
    }
    vehicle {
        ...VehicleSpecs
    }
    draftFlow {
        isReceived
        isDepositCompleted
        isSubmittedToBank
        isAppointmentCompleted
        isSubmittedToInsurer
        applicationSubmissionDate
        applicationDraftedDate
    }
}
