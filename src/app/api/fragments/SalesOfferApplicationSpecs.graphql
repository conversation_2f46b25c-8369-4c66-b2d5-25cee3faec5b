fragment SalesOfferApplicationSpecs on SalesOfferApplication {
    dealerId

    dealer {
        ...DealerJourneyData
    }

    module {
        ... on SalesOfferModule {
            ...SalesOfferModuleDebugJourney
        }
    }

    configuration {
        withFinancing
        withInsurance
    }

    applicantAgreements {
        ...ApplicationAgreementData
    }

    applicantKYC {
        ...KYCFieldSpecs
    }

    documents {
        ...ApplicationDocumentData
    }


    applicant {
        ...CustomerSpecs
    }

    draftFlow {
        ...JourneyDraftFlow
    }

    signing {
        ... on ApplicationNamirialSigning {
            ...NamirialSigningData
        }
    }

    dealer {
        id
        displayName
        legalName {
            ...TranslatedStringData
        }

        contact {
            telephone {
                value
                prefix
            }
            email
            address {
                ...TranslatedStringData
            }
        }
    }

    vehicle {
        ...ApplicationVariantSpec
    }

    documents {
        ...ApplicationDocumentData
    }

    journeySteps

    lead {
        ...LeadData
    }
}
