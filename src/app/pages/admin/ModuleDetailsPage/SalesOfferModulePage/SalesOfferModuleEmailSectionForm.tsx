import { ApolloError } from '@apollo/client';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import {
    EmailContentUpdateType,
    SalesOfferModuleDealerSpecificEmailContentInput,
    SalesOfferModuleWithPermissionsSpecsFragment,
    useUpdateSalesOfferModuleEmailContentMutation,
} from '../../../../api';
import { DealerWithPermissionsFragmentFragment } from '../../../../api/fragments/DealerWithPermissionsFragment';
import Form from '../../../../components/fields/Form';
import { useThemeComponents } from '../../../../themes/hooks';
import useHandleError from '../../../../utilities/useHandleError';
import SalesOfferModuleEmailSection from './SalesOfferModuleEmailSection';

type SalesOfferModuleEmailSectionFormProps = {
    module: SalesOfferModuleWithPermissionsSpecsFragment;
    dealer: DealerWithPermissionsFragmentFragment;
};

type SalesOfferEmailSectionFormValues = {
    emailContents: SalesOfferModuleDealerSpecificEmailContentInput;
};
const SalesOfferModuleEmailSectionForm = (props: SalesOfferModuleEmailSectionFormProps) => {
    const { module, dealer } = props;

    const { t } = useTranslation(['salesOfferModuleDetails']);

    const [mutation] = useUpdateSalesOfferModuleEmailContentMutation();
    const { notification } = useThemeComponents();
    if (module.__typename !== 'SalesOfferModule') {
        return null;
    }

    const initialValues: SalesOfferEmailSectionFormValues = {
        emailContents: {
            ...module.emailContents,
            dealerId: null,
            emailContentUpdateType: EmailContentUpdateType.Module,
        },
    };

    const onSubmit = useHandleError<SalesOfferEmailSectionFormValues>(
        async values => {
            try {
                // submitting message
                notification.loading({
                    content: t('salesOfferModuleDetails:messages.updateEmailSection'),
                    key: 'primary',
                    duration: 0,
                });

                await mutation({
                    variables: {
                        moduleId: module.id,
                        settings: values.emailContents,
                    },
                }).finally(() => {
                    notification.destroy('primary');
                });

                // inform about success
                notification.success({
                    content: t('salesOfferModuleDetails:messages.updateEmailSectionSuccessful'),
                    key: 'primary',
                });
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                }
            }
        },
        [module.id, mutation, notification, t]
    );

    return (
        <Formik<SalesOfferEmailSectionFormValues> initialValues={initialValues} onSubmit={onSubmit}>
            {({ handleSubmit }) => (
                <Form
                    id="salesOfferModuleEmailSection"
                    name="salesOfferModuleEmailSection"
                    onSubmitCapture={handleSubmit}
                >
                    <SalesOfferModuleEmailSection
                        companyId={module.company.id}
                        dealer={dealer}
                        defaultValueDisabled={false}
                        emailContents={module.emailContents}
                    />
                </Form>
            )}
        </Formik>
    );
};

export default SalesOfferModuleEmailSectionForm;
