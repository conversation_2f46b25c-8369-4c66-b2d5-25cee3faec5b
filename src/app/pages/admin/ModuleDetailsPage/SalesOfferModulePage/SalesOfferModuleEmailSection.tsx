import { Col, Row } from 'antd';
import { isNil } from 'lodash/fp';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../../shared/permissions';
import { DealerWithPermissionsFragmentFragment, SalesOfferModuleInDealerSpecsFragment } from '../../../../api';
import DealershipTranslatedTextAreaField from '../../../../components/fields/DealershipFields/DealershipTranslatedTextArea';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import hasPermissions from '../../../../utilities/hasPermissions';

type SalesOfferModuleEmailSectionProps = {
    dealer: DealerWithPermissionsFragmentFragment;
    emailContents: SalesOfferModuleInDealerSpecsFragment['emailContents'];
    targetDealerId?: string;
    companyId: string;
    defaultValueDisabled?: boolean;
};

const colSpan = {
    lg: 8,
    xs: 24,
};

type CommonSalesOfferEmailContentsProps = {
    companyId: string;
    defaultValueDisabled: boolean;
    disabled: boolean;
    targetDealerId: string;
    path: string;
};
const CommonSalesOfferEmailContents = ({
    companyId,
    defaultValueDisabled,
    disabled,
    targetDealerId,
    path,
}: CommonSalesOfferEmailContentsProps) => {
    const { t } = useTranslation(['salesOfferModuleDetails']);
    const getName = useCallback(deepPath => `${path}.${deepPath}`, [path]);

    return (
        <Row className="added-bottom-padding" gutter={[16, 16]}>
            <Col {...colSpan}>
                <DealershipTranslatedTextAreaField
                    {...t(`salesOfferModuleDetails:emailContents.common.subject`, { returnObjects: true })}
                    autoSize={{ minRows: 2, maxRows: 6 }}
                    companyId={companyId}
                    defaultValueDisabled={defaultValueDisabled}
                    disabled={disabled}
                    name={getName('subject')}
                    targetDealerId={targetDealerId}
                />
            </Col>

            <Col {...colSpan}>
                <DealershipTranslatedTextAreaField
                    {...t(`salesOfferModuleDetails:emailContents.common.introTitle`, { returnObjects: true })}
                    autoSize={{ minRows: 2, maxRows: 6 }}
                    companyId={companyId}
                    defaultValueDisabled={defaultValueDisabled}
                    disabled={disabled}
                    name={getName('introTitle')}
                    targetDealerId={targetDealerId}
                />
            </Col>

            <Col {...colSpan}>
                <DealershipTranslatedTextAreaField
                    {...t(`salesOfferModuleDetails:emailContents.common.contentText`, { returnObjects: true })}
                    companyId={companyId}
                    defaultValueDisabled={defaultValueDisabled}
                    disabled={disabled}
                    name={getName('contentText')}
                    targetDealerId={targetDealerId}
                />
            </Col>
        </Row>
    );
};

const SalesOfferModuleEmailSection = ({
    dealer,
    emailContents,
    targetDealerId,
    companyId,
    defaultValueDisabled,
}: SalesOfferModuleEmailSectionProps) => {
    const { t } = useTranslation(['salesOfferModuleDetails']);

    const hasUpdatePermission = !isNil(dealer)
        ? hasPermissions(dealer.permissions, [permissionKind.updateDealer])
        : true;
    const disabled = !hasUpdatePermission;

    return (
        <CollapsibleWrapper defaultActiveKey={['emailContents']}>
            <Panel
                key="emailContents"
                className="added-bottom-padding"
                header={t('salesOfferModuleDetails:emailContents.title')}
            >
                <CollapsibleWrapper defaultActiveKey={['combinedTemplate']}>
                    <Panel
                        key="combinedTemplate"
                        header={t('salesOfferModuleDetails:emailContents.combinedTemplate.label')}
                    >
                        <CommonSalesOfferEmailContents
                            companyId={companyId}
                            defaultValueDisabled={defaultValueDisabled}
                            disabled={disabled}
                            path="emailContents.combinedTemplate"
                            targetDealerId={targetDealerId}
                        />
                    </Panel>
                </CollapsibleWrapper>
                <CollapsibleWrapper defaultActiveKey={['singlePreOfferTemplate']}>
                    <Panel
                        key="singlePreOfferTemplate"
                        header={t('salesOfferModuleDetails:emailContents.singlePreOfferTemplate.title')}
                    >
                        <CommonSalesOfferEmailContents
                            companyId={companyId}
                            defaultValueDisabled={defaultValueDisabled}
                            disabled={disabled}
                            path="emailContents.singlePreOfferTemplate"
                            targetDealerId={targetDealerId}
                        />
                    </Panel>
                </CollapsibleWrapper>

                <CollapsibleWrapper defaultActiveKey={['salesOfferTemplate']}>
                    <Panel
                        key="salesOfferTemplate"
                        header={t('salesOfferModuleDetails:emailContents.salesOfferTemplate.label')}
                    >
                        <CommonSalesOfferEmailContents
                            companyId={companyId}
                            defaultValueDisabled={defaultValueDisabled}
                            disabled={disabled}
                            path="emailContents.salesOfferTemplate"
                            targetDealerId={targetDealerId}
                        />
                    </Panel>
                </CollapsibleWrapper>
            </Panel>
        </CollapsibleWrapper>
    );
};

export default SalesOfferModuleEmailSection;
