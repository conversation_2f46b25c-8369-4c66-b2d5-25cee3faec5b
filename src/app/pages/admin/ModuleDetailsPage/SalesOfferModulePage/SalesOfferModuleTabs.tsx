// eslint-disable-next-line max-len
import { SalesOfferModuleWithPermissionsSpecsFragment } from '../../../../api/fragments/SalesOfferModuleWithPermissionsSpecs';
import SalesOfferModuleEmailSection from './SalesOfferModuleEmailSectionForm';
import SalesOfferModuleDetailsInner from './SalesOfferModuleMainDetails';
import { HeaderTabs } from './enums';

type SalesOfferModuleTabsProps = {
    activeTabs: HeaderTabs;
    module: SalesOfferModuleWithPermissionsSpecsFragment;
};
const SalesOfferModuleTabs = ({ activeTabs, module }: SalesOfferModuleTabsProps) => {
    switch (activeTabs) {
        case HeaderTabs.MainDetials:
            return <SalesOfferModuleDetailsInner module={module} />;

        case HeaderTabs.EmailContents:
            return <SalesOfferModuleEmailSection dealer={null} module={module} />;

        default:
            return null;
    }
};

export default SalesOfferModuleTabs;
