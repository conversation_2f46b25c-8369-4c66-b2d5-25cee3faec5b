import {
    CounterMethod,
    DealerTranslatedStringSetting,
    ModuleType,
    SalesOfferEmailContents,
    SalesOfferModule,
} from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

const defaultEmptyTranslatedString: DealerTranslatedStringSetting = {
    defaultValue: { defaultValue: '', overrides: [] },
    overrides: [],
};

const defaultEmptyEmailContent: SalesOfferEmailContents = {
    contentText: defaultEmptyTranslatedString,
    subject: defaultEmptyTranslatedString,
    introTitle: defaultEmptyTranslatedString,
};

export default {
    identifier: '282_addSalesOfferModuleEmailContent',
    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        // adding default for sales offer module
        await db.collection<SalesOfferModule>('modules').updateMany(
            {
                _type: ModuleType.SalesOfferModule,
            },
            {
                $set: {
                    vsaCounter: {
                        method: CounterMethod.Yearly,
                        prefix: 'VSA{YY}',
                        padding: 4,
                    },
                    specificationTerms: defaultEmptyTranslatedString,
                    vsaSigningInstructions: defaultEmptyTranslatedString,
                    vsaTerms: defaultEmptyTranslatedString,
                    coeBiddingTerms: defaultEmptyTranslatedString,
                    emailContents: {
                        combinedTemplate: defaultEmptyEmailContent,
                        salesOfferTemplate: defaultEmptyEmailContent,
                        singlePreOfferTemplate: defaultEmptyEmailContent,
                    },
                },
            }
        );
    },
};
