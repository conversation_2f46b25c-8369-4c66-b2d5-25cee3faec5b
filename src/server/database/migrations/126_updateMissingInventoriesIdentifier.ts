import { AnyBulkWriteOperation } from 'mongodb';
import { Inventory } from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';
import { increaseCompanyCounter } from '../helpers/counters';

export default {
    identifier: '126_updateMissingInventoriesIdentifier',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        // update the empty identifier
        const inventories = await db.collection<Inventory>('inventories').find({ identifier: '' }).toArray();

        const promises = await Promise.all(
            inventories
                .map(async (inventory): Promise<AnyBulkWriteOperation | null> => {
                    const module = await db.collection('modules').findOne({ _id: inventory.moduleId });

                    if (!module) {
                        return null;
                    }

                    const counter = await increaseCompanyCounter(module.companyId, 'INV');

                    const identifier = `INV${counter.toString().padStart(5, '0')}`;

                    return {
                        updateOne: {
                            filter: { _id: inventory._id },
                            update: { $set: { identifier } },
                        },
                    };
                })
                .filter(Boolean)
        );

        if (promises.length) {
            await db.collection('inventories').bulkWrite(promises);
        }
    },
};
