import type { SalesOffer } from '../documents';
import { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '283_updateTradeInSalesOfferLastUpdatedAt',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        const salesOfferCollection = db.collection<SalesOffer>('salesOffers');
        const salesOffers = await salesOfferCollection
            .find({
                $or: [{ 'tradeIn.lastUpdatedAt': { $exists: false } }, { 'tradeIn.lastUpdatedAt': { $eq: null } }],
            })
            .toArray();

        const operations = salesOffers.map(salesOffer => ({
            updateOne: {
                filter: { _id: salesOffer._id },
                update: {
                    $set: {
                        'tradeIn.lastUpdatedAt': new Date(),
                    },
                },
            },
        }));

        if (operations.length > 0) {
            await salesOfferCollection.bulkWrite(operations);
        }
    },
};
