import { ObjectId } from 'mongodb';
import { ApplicationCore, ApplicationDocument, ApplicationKind, OtherVehicleInformation } from '../core';
import { ApplicationFinancing } from '../financing';
import { ApplicationInsurancing } from '../insurancing';
import { SalesOfferApplicationConfiguration } from '../shared';

export type SalesOfferApplication = ApplicationCore<ApplicationKind.SalesOffer> & {
    // vehicle ID
    vehicleId: ObjectId;

    bankId?: ObjectId;

    // customer/applicant ID
    applicantId: ObjectId;

    // endpoint & router IDs
    routerId?: ObjectId | null;
    endpointId?: ObjectId | null;

    // assigned dealer ID
    dealerId: ObjectId;

    otherVehicleInformation?: OtherVehicleInformation;

    // miscellaneous configuration
    configuration: SalesOfferApplicationConfiguration;

    // financing setting
    financing?: ApplicationFinancing | null;

    // insurancing setting
    insurancing?: ApplicationInsurancing | null;

    // documents
    documents: ApplicationDocument[];

    leadId: ObjectId;
};
