import { isNil, isEmpty } from 'lodash/fp';
import { getDealershipPaymentSettingId } from '../../utils/getDealershipPaymentSetting';

import { DraftingStep } from '../common';
import { getDepositStep } from '../legacyJourney/makeLegacyJourney';
import SalesOfferJourneyContext from './SalesOfferJourneyContext';

const makeSalesOfferJourney = async (context: SalesOfferJourneyContext) => {
    const { application, journey: journeyState, includeWholeJourney } = context;

    let journey = new DraftingStep(context);

    const hasPaymentStep =
        !isEmpty(application.reservationStage) &&
        (!journeyState.deposit?.completed || includeWholeJourney) &&
        (!journeyState.deposit?.skipped || includeWholeJourney) &&
        !isNil(getDealershipPaymentSettingId(application.dealerId, context.applicationModule));

    if (hasPaymentStep) {
        const depositStep = await getDepositStep(context);

        if (depositStep) {
            journey = journey.append(depositStep);
        }
    }

    return journey.first;
};

export default makeSalesOfferJourney;
