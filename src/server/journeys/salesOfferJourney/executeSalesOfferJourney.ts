import { SalesOfferApplication, User } from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import { InvalidPermission } from '../../schema/errors';
import generateJourneyToken, { JourneyTokenPayload } from '../generateJourneyToken';
import SalesOfferJourneyContext from './SalesOfferJourneyContext';
import makeSalesOfferJourney from './makeSalesOfferJourney';

export type ExecuteSalesOfferJourneyArgs<Payload> = {
    application: SalesOfferApplication | null;
    user: User | null;
    identifier: string;
    origin: JourneyTokenPayload['origin'];
    payload: Payload;
};

const executeSalesOfferJourney = async <Payload>({
    application,
    identifier,
    user,
    origin,
    payload,
}: ExecuteSalesOfferJourneyArgs<Payload>) => {
    const context = await SalesOfferJourneyContext.factory(application, user, origin);

    if (context.journey.isReceived) {
        // application is already received, nothing may be done anymore
        throw new InvalidPermission();
    }

    const controller = await makeSalesOfferJourney(context);

    // get the step
    const step = controller.find<Payload>(identifier);

    if (!step.mayBeExecuted) {
        // it's too early or too late to execute it
        throw new InvalidPermission();
    }

    // then execute
    await step.execute(payload);

    // generate a new token
    const { collections } = await getDatabaseContext();
    const company = await collections.companies.findOne({ _id: context.applicationModule.companyId });

    const token = generateJourneyToken(context.lead, context.application, origin, company.sessionTimeout, user);

    return {
        lead: context.lead,
        application: context.application,
        token,
    };
};

export default executeSalesOfferJourney;
