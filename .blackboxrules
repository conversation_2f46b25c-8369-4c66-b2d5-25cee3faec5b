# Fundamental Principles
- Write clean, simple, readable code
- Avoid simply agreeing with my points or taking my conclusions at face value. I want a real intellectual challenge, not affirmation
- Implement features in the simplest possible way
- Keep files small and focused (<200 lines)
- Test after every meaningful change
- Focus on core functionalitv before optimization
- Use clear, consistent naming
- Think thoroughly before coding. Write 2-3 reasoning paragraphs.
- ALWAYS write simple, clean and modular code.
- use clear and easy-to-understand language. write in short sentences.
- find any script that might resolve error if you stuck on an error
- dont use "any" type, find relevant type on existing codebase
- ensure no circular dependency is created
- When I say "it is not working" (or similar phrases like "it's not working," "doesn't work," etc.), generate a structured debug log template for me to fill out, and analyze it later when I provide the completed log to identify the root cause.

# Error Fixing
- DO NOT JUMP TO CONCLUSIONS! Consider multiple possible causes before deciding.
- Explain the problem in plain English
- Make minimal necessary changes, changing as few lines of code as possible
- in case of strange errors, ask the user to perform a Perplexity web search to find the latest up-to-date information

# Building Process
- Verify each new feature works by telling the user how to test it
- DO NOT write complicated and confusing code. Opt for the simple & modular approach.
- when not sure what to do, tell the user to perform a web search

# Comments
- Generate comments that explain the intent , trade-offs , or business logic behind the code (e.g., 'Handles rate-limiting from Third-Party API X to avoid downtime')
- Write comments in a natural, team-specific tone (e.g., 'Temporary hack until v3 release' or 'Avoid refactoring unless critical)
- Include notes about edge cases , technical debt , or external dependencies (e.g., 'Workaround for legacy system Y’s missing validation')
- Skip obvious explanations (e.g., 'Increments i by 1'). Focus on non-trivial decisions (e.g., 'Uses binary search to meet O(log n) requirements for large datasets')
- NEVER delete old comments - unless they are obviously wrong / obsolete

# Working with git
- always follow conventional commit rules for the commit message. Be clear and concise with the commit message
- ensure one commit only do one thing